import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/api_client/api_client_impl.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/navigation/guards/auth_guard.dart';
import 'package:melodyze/core/navigation/guards/force_update_guard.dart';
import 'package:melodyze/core/navigation/guards/preference_guard.dart';
import 'package:melodyze/core/services/file_manager/file_manager_service.dart';

import 'package:melodyze/core/wrappers/analytics/crash_logger.dart';
import 'package:melodyze/core/wrappers/analytics/user_event.dart';
import 'package:melodyze/core/wrappers/app_toast.dart';
import 'package:melodyze/core/wrappers/event_bus.dart';
import 'package:melodyze/core/wrappers/firebase_cloud_messaging.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/local_notification.dart';
import 'package:melodyze/core/wrappers/local_storage_helper.dart';
import 'package:melodyze/core/wrappers/remote_config_helper.dart';
import 'package:melodyze/core/wrappers/secure_storage_helper.dart';
import 'package:melodyze/firebase_options.dart';
import 'package:melodyze/modules/auth/auth_repository.dart';
import 'package:melodyze/modules/auth/bloc/authentication_bloc.dart';
import 'package:melodyze/modules/profile/bloc/profile_bloc.dart';
import 'package:melodyze/modules/profile/repo/profile_repo.dart';
import 'package:melodyze/modules/profile/serivce/profile_service.dart';
import 'package:melodyze/modules/share/cubit/fcm_notification_data_cubit.dart';

Future<void> initialiseConfig() async {
  WidgetsFlutterBinding.ensureInitialized(); // Required by FlutterConfig
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  await FirebaseCloudMessaging().init();
  DI().registerSingleton<CrashLogger>(CrashLogger()..initialiseErrorHandlers());
  await RemoteConfigHelper.init();
  DI().registerSingleton<FcmNotificationDataCubit>(FcmNotificationDataCubit());
  // FirebaseMessaging.onBackgroundMessage(handleBackgroundMessage);
  final message = await FirebaseMessaging.instance.getInitialMessage();
  if (message != null) {
    DI().resolve<FcmNotificationDataCubit>().setNotificationDataWithoutEmit(message.data);
  }
  await LocalNotification().init();
}

Future<void> initialiseDependencies() async {
  DI().registerSingleton<ApiClient>(ApiClientImpl());
  DI().registerSingleton<SecureStorageHelper>(SecureStorageHelper());
  DI().registerSingleton<AccessTokenHelper>(AccessTokenHelper());

  final localStorageHelper = await LocalStorageHelper.createInstance();
  DI().registerSingleton<LocalStorageHelper>(localStorageHelper);

  final fileManagerService = FileManagerService.instance;
  await fileManagerService.initialize();
  DI().registerSingleton<FileManagerService>(fileManagerService);

  DI().registerSingleton<AppToast>(AppToast());
  DI().registerSingleton<EventBus>(EventBus());
  DI().registerSingleton<EventSDKProvider>(EventSDK());
  DI().registerLazySingleton<ProfileBloc>(() => ProfileBloc(profileRepo: ProfileRepo(profileService: ProfileService())));
  DI().registerSingleton<FirstLoginHelper>(FirstLoginHelper());
  DI().registerSingleton<AuthenticationBloc>(AuthenticationBloc(authRepository: AuthRepository()));
  DI().registerSingleton<AuthGuard>(AuthGuard(secureStorageHelper: DI().resolve<SecureStorageHelper>()));
  DI().registerSingleton<ForceUpdateGuard>(ForceUpdateGuard());
  DI().registerSingleton<PreferenceGuard>(PreferenceGuard(secureStorageHelper: DI().resolve<SecureStorageHelper>()));
  DI().registerLazySingleton<AppRouter>(
    () => AppRouter(
      authGuard: DI().resolve<AuthGuard>(),
      forceUpdateGuard: DI().resolve<ForceUpdateGuard>(),
      preferenceGuard: DI().resolve<PreferenceGuard>(),
    ),
  );
}
